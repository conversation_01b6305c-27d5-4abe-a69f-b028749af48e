CRYPTO ASSISTANT - TECH<PERSON>CAL INDICATORS DISPLAY & SCORING SYSTEM
===============================================================================

OVERVIEW
--------
The technical indicators are displayed in two formats:
1. COMPACT VIEW: Shows status only (Bullish/Bearish/Neutral) in the above-the-fold section
2. DETAILED VIEW: Shows full indicator details sorted by points in the below-the-fold section

The detailed view sorts indicators by their calculated points (highest to lowest) to show 
the most significant indicators first.

DETAILED TECHNICAL INDICATORS SORTING & SCORING
===============================================================================

DISPLAY ORDER
-------------
Indicators are sorted by:
1. PRIMARY: Points (highest first)
2. SECONDARY: Name (alphabetical for ties)

Each indicator shows:
- Indicator name
- Points earned (with "pts" label)
- Status (BULLISH/BEARISH/NEUTRAL with color coding)
- Detailed values and calculations

SCORING SYSTEM FOR EACH INDICATOR
===============================================================================

1. RSI (RELATIVE STRENGTH INDEX)
   Maximum Points: 25
   
   EXTREME CONDITIONS (Full Points):
   - RSI < 30 (Oversold): 25 BULLISH points
   - RSI > 70 (Overbought): 25 BEARISH points
   
   MODERATE CONDITIONS (Scaled Points):
   - RSI 30-70: Points = |RSI - 50| / 2 (0-10 points)
     * RSI > 50: BULLISH status
     * RSI < 50: BEARISH status
   
   Example:
   - RSI = 39.5: Points = |39.5 - 50| / 2 = 5.25 → 5 BEARISH points
   - RSI = 65: Points = |65 - 50| / 2 = 7.5 → 8 BULLISH points

2. MACD (MOVING AVERAGE CONVERGENCE DIVERGENCE)
   Maximum Points: 20
   
   STRONG SIGNALS (Full Points):
   - MACD > Signal AND Histogram > 0: 20 BULLISH points
   - MACD < Signal AND Histogram < 0: 20 BEARISH points
   
   WEAK SIGNALS (Scaled Points):
   - Mixed conditions: Points = min(|Histogram| × 10000, 15)
     * MACD > Signal: BULLISH status
     * MACD < Signal: BEARISH status
   
   Example:
   - MACD: -0.005828, Signal: -0.004609, Histogram: -0.00122
   - Since MACD < Signal AND Histogram < 0: 20 BEARISH points

3. EMA TREND (EMA20 vs EMA50)
   Fixed Points: 15
   
   CONDITIONS (Always Awards Points):
   - EMA20 > EMA50: 15 BULLISH points
   - EMA20 < EMA50: 15 BEARISH points
   
   Example:
   - EMA20: $0.80151296, EMA50: $0.81539769
   - Since EMA20 < EMA50: 15 BEARISH points

4. BOLLINGER BANDS
   Maximum Points: 25
   
   EXTREME CONDITIONS (Full Points):
   - Price > Upper Band: 25 BEARISH points (Overbought)
   - Price < Lower Band: 25 BULLISH points (Oversold)
   
   MODERATE CONDITIONS (Scaled Points):
   - Position within bands = (Price - Lower) / (Upper - Lower)
   - If Position > 0.5: Points = (Position - 0.5) × 2 × 20 (0-20 BEARISH points)
   - If Position < 0.5: Points = (0.5 - Position) × 2 × 20 (0-20 BULLISH points)
   
   Example:
   - Price: $0.7828, Upper: $0.82426569, Lower: $0.78081431
   - Position = (0.7828 - 0.78081431) / (0.82426569 - 0.78081431) = 0.046
   - Since Position < 0.5: Points = (0.5 - 0.046) × 2 × 20 = 18.16 → 18 BULLISH points

DISPLAY FORMAT
===============================================================================

COMPACT VIEW (Above-the-fold):
------------------------------
Shows 4 indicators in a 2-column grid:
- RSI: Bullish/Bearish/Neutral
- MACD: Bullish/Bearish
- EMA Cross: Bullish/Bearish
- Bollinger: Bullish/Bearish/Neutral

DETAILED VIEW (Below-the-fold):
-------------------------------
Shows indicators sorted by points with full details:

Format for each indicator:
┌─────────────────────────────────────┐
│ [Indicator Name]        [X pts]     │
│                        [STATUS]     │
│ ─────────────────────────────────── │
│ [Detailed Values]                   │
│ [Additional Metrics]                │
└─────────────────────────────────────┘

Example Display Order (based on sample data):
1. MACD - 20 pts - BEARISH
2. Bollinger Bands - 18 pts - BULLISH
3. EMA Trend - 15 pts - BEARISH
4. RSI - 5 pts - BEARISH

COLOR CODING
===============================================================================
- BULLISH: Green text (#10B981)
- BEARISH: Red text (#EF4444)
- NEUTRAL: Yellow text (#F59E0B)
- Points Badge: Blue background for active points, Gray for zero points

TECHNICAL IMPLEMENTATION
===============================================================================
- Indicators are calculated in calculateIndicatorPoints() function
- Each indicator returns: name, points, status, type
- Array is sorted by points (descending), then by name (ascending)
- renderIndicatorDetails() function handles the specific display for each type
- Real-time updates maintain the same sorting and scoring logic
