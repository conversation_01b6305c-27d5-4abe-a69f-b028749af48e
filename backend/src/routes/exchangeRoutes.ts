import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
// import * as exchangeController from '../controllers/exchangeController';
import * as exchangeController from '../controllers/exchangeController';

const router = Router();

// Exchange information routes
router.get('/', async<PERSON><PERSON><PERSON>(exchangeController.getSupportedExchanges));
router.get('/:exchange/info', async<PERSON><PERSON><PERSON>(exchangeController.getExchangeInfo));
router.get('/:exchange/status', async<PERSON>andler(exchangeController.getExchangeStatus));
router.get('/:exchange/markets', async<PERSON>and<PERSON>(exchangeController.getExchangeMarkets));

// Price data routes
router.get('/:exchange/ticker/:symbol', async<PERSON>and<PERSON>(exchangeController.getTicker));
router.get('/:exchange/tickers', async<PERSON>and<PERSON>(exchangeController.getAllTickers));
router.get('/:exchange/orderbook/:symbol', async<PERSON>and<PERSON>(exchangeController.getOrderBook));
router.get('/:exchange/trades/:symbol', async<PERSON><PERSON><PERSON>(exchangeController.getRecentTrades));

// OHLCV data routes
router.get('/:exchange/ohlcv/:symbol', asyncHandler(exchangeController.getOHLCV));
router.get('/:exchange/candles/:symbol/:timeframe', asyncHandler(exchangeController.getCandles));

// Multi-exchange comparison routes
router.get('/compare/prices/:symbol', asyncHandler(exchangeController.comparePrices));
router.get('/compare/spreads/:symbol', asyncHandler(exchangeController.getSpreadAnalysis));
router.get('/arbitrage/opportunities', asyncHandler(exchangeController.getArbitrageOpportunities));

// Exchange connectivity routes
router.get('/:exchange/ping', asyncHandler(exchangeController.pingExchange));
router.get('/health-check', asyncHandler(exchangeController.healthCheckAllExchanges));

export default router;
