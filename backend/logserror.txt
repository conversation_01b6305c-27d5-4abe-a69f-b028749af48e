2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(110,90): error TS2304: Cannot find name 'candles'.\nsrc/Services/binanceService.ts(112,17): error TS2304: Cannot find name 'Failed'.\nsrc/Services/binanceService.ts(112,24): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(112,27): error TS2304: Cannot find name 'initialize'.\nsrc/Services/binanceService.ts(112,38): error TS2304: Cannot find name 'kline'.\nsrc/Services/binanceService.ts(112,44): error TS2304: Cannot find name 'buffer'.\nsrc/Services/binanceService.ts(112,55): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(112,56): error TS2872: This kind of expression is always truthy.\nsrc/Services/binanceService.ts(112,57): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(112,65): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(112,67): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(119,18): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(119,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(119,27): error TS2304: Cannot find name '_$'.\nsrc/Services/binanceService.ts(119,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(121,20): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(121,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(123,11): error TS2451: Cannot redeclare block-scoped variable 'ws'.\nsrc/Services/binanceService.ts(123,30): error TS2304: Cannot find name 'wsUrl'.\nsrc/Services/binanceService.ts(126,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(126,62): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(132,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(132,32): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(132,40): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(134,63): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(134,73): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(139,45): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(139,55): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(143,56): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(143,66): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(144,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(144,36): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(148,14): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(148,40): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(149,11): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(149,42): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(149,50): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(154,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(154,31): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(158,11): error TS2304: Cannot find name 'handleKlineUpdate'.\nsrc/Services/binanceService.ts(158,29): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(158,37): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(158,45): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(158,56): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(158,64): error TS2304: Cannot find name 'message'.\nsrc/Services/binanceService.ts(158,73): error TS2693: 'any' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(159,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(159,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(159,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(160,11): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(160,23): error TS2304: Cannot find name 'message'.\nsrc/Services/binanceService.ts(162,9): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(162,10): error TS7006: Parameter 'klineData' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(163,51): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(163,61): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(168,11): error TS2304: Cannot find name 'currentBuffer'.\nsrc/Services/binanceService.ts(168,27): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(168,47): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(171,11): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(172,7): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(173,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(174,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(175,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(176,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(177,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(181,5): error TS18004: No value exists in scope for the shorthand property 'let'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(181,29): error TS2304: Cannot find name 'currentBuffer'.\nsrc/Services/binanceService.ts(183,9): error TS7006: Parameter 'klineData' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(185,7): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(185,26): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(188,11): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(188,34): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(189,9): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(189,25): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(189,46): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(193,11): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(194,9): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(194,23): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(194,51): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(196,9): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(196,28): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(201,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(201,25): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(201,30): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(204,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(204,33): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(204,38): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(206,40): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(206,50): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(206,77): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(206,110): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(210,3): error TS2304: Cannot find name 'getCachedOHLCV'.\nsrc/Services/binanceService.ts(210,18): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(210,26): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(210,34): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(210,45): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(210,53): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(210,60): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(210,75): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(211,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(211,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(212,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(219,3): error TS2304: Cannot find name 'hasKlineData'.\nsrc/Services/binanceService.ts(219,16): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(219,24): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(219,32): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(219,43): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(219,52): error TS2693: 'boolean' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(220,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(220,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(221,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(226,3): error TS2304: Cannot find name 'subscribeToKlineUpdates'.\nsrc/Services/binanceService.ts(226,27): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(226,35): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(226,43): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(226,54): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(226,62): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(227,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(227,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(227,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(229,9): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(229,10): error TS2680: A 'this' parameter must be the first parameter.\nsrc/Services/binanceService.ts(229,36): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(230,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(230,33): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(233,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(233,31): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(233,41): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(236,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(236,40): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(238,7): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(243,3): error TS2304: Cannot find name 'unsubscribeFromKlineUpdates'.\nsrc/Services/binanceService.ts(243,31): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(243,39): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(243,47): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(243,58): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(243,66): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(244,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(244,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(244,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(245,11): error TS2304: Cannot find name 'subscribers'.\nsrc/Services/binanceService.ts(245,25): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(245,51): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(247,9): error TS7006: Parameter 'subscribers' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(248,26): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(252,14): error TS2339: Property 'klineSubscribers' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(252,38): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(255,25): error TS2339: Property 'klineConnections' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(255,46): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(258,16): error TS2339: Property 'klineConnections' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(258,40): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(259,16): error TS2339: Property 'klineCache' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(259,34): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(260,50): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(260,60): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(267,11): error TS2304: Cannot find name 'notifyKlineSubscribers'.\nsrc/Services/binanceService.ts(267,34): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(267,39): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(267,47): error TS2304: Cannot find name 'ohlcv'.\nsrc/Services/binanceService.ts(267,54): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(268,11): error TS2304: Cannot find name 'subscribers'.\nsrc/Services/binanceService.ts(268,25): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(268,51): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(269,9): error TS7006: Parameter 'subscribers' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(270,27): error TS7006: Parameter 'callback' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(272,20): error TS2304: Cannot find name 'ohlcv'.\nsrc/Services/binanceService.ts(274,60): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(281,3): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(281,9): error TS2304: Cannot find name 'getAllSymbols'.\nsrc/Services/binanceService.ts(281,34): error TS2693: 'BinanceSymbol' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(281,51): error TS2349: This expression is not callable.\n  Type '{ try: { logDebug(: any): any; const: any; logInfo(: any, symbols: any): any; \"\": any; }; trading: any; symbols: any; from: any; Binance: any; }' has no call signatures.\nsrc/Services/binanceService.ts(283,16): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(285,14): error TS2304: Cannot find name 'exchangeInfo'.\nsrc/Services/binanceService.ts(285,28): error TS2304: Cannot find name 'ticker24hr'.\nsrc/Services/binanceService.ts(286,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(287,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(290,13): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(290,23): error TS2304: Cannot find name 'exchangeInfo'.\nsrc/Services/binanceService.ts(300,30): error TS2304: Cannot find name 'ticker24hr'.\nsrc/Services/binanceService.ts(315,15): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(315,26): error TS7006: Parameter 'symbols' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(315,42): error TS18004: No value exists in scope for the shorthand property 'trading'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(315,50): error TS18004: No value exists in scope for the shorthand property 'symbols'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(315,58): error TS18004: No value exists in scope for the shorthand property 'from'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(315,63): error TS18004: No value exists in scope for the shorthand property 'Binance'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(334,17): error TS2304: Cannot find name 'No'.\nsrc/Services/binanceService.ts(334,27): error TS2304: Cannot find name 'price'.\nsrc/Services/binanceService.ts(334,33): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(334,38): error TS2304: Cannot find name 'available'.\nsrc/Services/binanceService.ts(334,52): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(334,53): error TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.\nsrc/Services/binanceService.ts(334,54): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(334,64): error TS2363: The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.\nsrc/Services/binanceService.ts(334,74): error TS2304: Cannot find name 'may'.\nsrc/Services/binanceService.ts(334,78): error TS2304: Cannot find name 'not'.\nsrc/Services/binanceService.ts(334,82): error TS2304: Cannot find name 'have'.\nsrc/Services/binanceService.ts(334,87): error TS2304: Cannot find name 'received'.\nsrc/Services/binanceService.ts(334,96): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(334,101): error TS2304: Cannot find name 'yet'.\nsrc/Services/binanceService.ts(335,24): error TS2304: Cannot find name 'No'.\nsrc/Services/binanceService.ts(335,34): error TS2304: Cannot find name 'price'.\nsrc/Services/binanceService.ts(335,40): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(335,45): error TS2304: Cannot find name 'available'.\nsrc/Services/binanceService.ts(335,59): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(335,60): error TS2349: This expression is not callable.\n  Type '{ symbol: any; }' has no call signatures.\nsrc/Services/binanceService.ts(335,61): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(337,23): error TS2304: Cannot find name 'getting'.\nsrc/Services/binanceService.ts(337,38): error TS2304: Cannot find name 'price'.\nsrc/Services/binanceService.ts(337,48): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(337,49): error TS2349: This expression is not callable.\n  Type '{ symbol: any; }' has no call signatures.\nsrc/Services/binanceService.ts(337,50): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(345,41): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(345,43): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(345,56): error TS2349: This expression is not callable.\n  Type 'RegExp' has no call signatures.\nsrc/Services/binanceService.ts(363,23): error TS2304: Cannot find name 'fetching'.\nsrc/Services/binanceService.ts(363,34): error TS2304: Cannot find name 'hr'.\nsrc/Services/binanceService.ts(363,37): error TS2304: Cannot find name 'ticker'.\nsrc/Services/binanceService.ts(363,48): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(363,49): error TS2349: This expression is not callable.\n  Type '{ symbol: any; }' has no call signatures.\nsrc/Services/binanceService.ts(363,50): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(382,15): error TS2304: Cannot find name 'Retrieved'.\nsrc/Services/binanceService.ts(382,25): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(382,27): error TS2304: Cannot find name 'tickers'.\nsrc/Services/binanceService.ts(382,43): error TS2304: Cannot find name 'tickers'.\nsrc/Services/binanceService.ts(382,51): error TS2304: Cannot find name 'from'.\nsrc/Services/binanceService.ts(382,66): error TS2304: Cannot find name 'cache'.\nsrc/Services/binanceService.ts(416,17): error TS2304: Cannot find name 'Fetching'.\nsrc/Services/binanceService.ts(416,26): error TS2448: Block-scoped variable 'klines' used before its declaration.\nsrc/Services/binanceService.ts(416,37): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(416,38): error TS2872: This kind of expression is always truthy.\nsrc/Services/binanceService.ts(416,39): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(416,47): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(416,49): error TS2448: Block-scoped variable 'interval' used before its declaration.\nsrc/Services/binanceService.ts(427,41): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(427,43): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(444,45): error TS2304: Cannot find name 'retryCount'.\nsrc/Services/binanceService.ts(445,35): error TS2304: Cannot find name 'retryCount'.\nsrc/Services/binanceService.ts(446,38): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(446,81): error TS2304: Cannot find name 'retryCount'.\nsrc/Services/binanceService.ts(451,45): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(457,11): error TS2304: Cannot find name 'convertTimeframeToInterval'.\nsrc/Services/binanceService.ts(457,38): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(457,49): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(457,58): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(470,3): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(470,9): error TS2304: Cannot find name 'getOHLCV'.\nsrc/Services/binanceService.ts(471,5): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(471,13): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(472,5): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(472,16): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(473,5): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(473,12): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(474,6): error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ try: { if(this: { if(this: ...): any; \"\": any; }): any; \"\": any; }; }'.\nsrc/Services/binanceService.ts(474,14): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(477,29): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(477,37): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(478,28): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(478,48): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(478,56): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(478,67): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(479,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(480,93): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(480,103): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(480,149): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(485,38): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(485,48): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(486,13): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(486,39): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(486,47): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(489,26): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(489,46): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(489,54): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(489,65): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(491,101): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(491,111): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(496,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(497,49): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(497,59): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(497,138): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(498,24): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(498,56): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(499,28): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(499,43): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(499,61): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(510,43): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(516,3): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(516,9): error TS2304: Cannot find name 'preSubscribeToKlineData'.\nsrc/Services/binanceService.ts(516,33): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(516,42): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(516,52): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(516,64): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(516,75): error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ logInfo(: any, symbols: any): any; \"\": any; }'.\nsrc/Services/binanceService.ts(516,83): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(517,13): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(517,50): error TS7006: Parameter 'symbols' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(517,66): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(517,74): error TS2304: Cannot find name 'and'.\nsrc/Services/binanceService.ts(517,78): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(517,80): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(517,99): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(525,23): error TS2304: Cannot find name 'Failed'.\nsrc/Services/binanceService.ts(525,30): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(525,33): error TS2304: Cannot find name 'pre'.\nsrc/Services/binanceService.ts(525,37): error TS2304: Cannot find name 'subscribe'.\nsrc/Services/binanceService.ts(525,47): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(525,50): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(525,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(525,60): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(525,62): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(543,14): error TS2304: Cannot find name 'Completed'.\nsrc/Services/binanceService.ts(543,24): error TS2304: Cannot find name 'pre'.\nsrc/Services/binanceService.ts(543,28): error TS2304: Cannot find name 'subscription'.\nsrc/Services/binanceService.ts(543,41): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(543,44): error TS2304: Cannot find name 'kline'.\nsrc/Services/binanceService.ts(543,50): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(543,59): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(543,60): error TS2872: This kind of expression is always truthy.\nsrc/Services/binanceService.ts(543,61): error TS18004: No value exists in scope for the shorthand property 'symbols'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(543,77): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(543,85): error TS2304: Cannot find name 'and'.\nsrc/Services/binanceService.ts(543,89): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(543,91): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(543,110): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(589,20): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(589,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(590,11): error TS2451: Cannot redeclare block-scoped variable 'ws'.\nsrc/Services/binanceService.ts(590,30): error TS2304: Cannot find name 'wsUrl'.\nsrc/Services/binanceService.ts(594,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(623,15): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(627,15): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(632,11): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(632,46): error TS7006: Parameter 'callback' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(649,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(654,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(655,24): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(658,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(662,3): error TS2304: Cannot find name 'subscribeToIndividualTicker'.\nsrc/Services/binanceService.ts(662,31): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(662,39): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(663,37): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(666,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(670,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(670,40): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(674,53): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(680,46): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(692,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(692,34): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(695,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(695,32): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(697,57): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(702,47): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(706,58): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(707,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(708,24): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(708,57): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(711,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(715,3): error TS2304: Cannot find name 'subscribeToPrice'.\nsrc/Services/binanceService.ts(715,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(715,28): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(715,36): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(716,10): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(716,31): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(717,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(717,28): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(719,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(719,40): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(721,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(721,26): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(721,39): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(724,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(724,45): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(726,7): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(730,38): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(735,3): error TS2304: Cannot find name 'unsubscribeFromPrice'.\nsrc/Services/binanceService.ts(735,24): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(735,32): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(735,40): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(736,31): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(736,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(738,32): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(740,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(740,33): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(746,11): error TS2304: Cannot find name 'notifySubscribers'.\nsrc/Services/binanceService.ts(746,29): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(746,37): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(746,45): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(746,51): error TS2693: 'BinanceTicker' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(747,31): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(747,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(749,33): error TS7006: Parameter 'callback' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(751,20): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(753,58): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(760,3): error TS2304: Cannot find name 'getCachedPrice'.\nsrc/Services/binanceService.ts(760,18): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(760,26): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(760,35): error TS2693: 'BinanceTicker' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(760,51): error TS18050: The value 'null' cannot be used here.\nsrc/Services/binanceService.ts(765,3): error TS2304: Cannot find name 'closeConnections'.\nsrc/Services/binanceService.ts(766,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(766,33): error TS7006: Parameter 'ws' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(766,37): error TS7006: Parameter 'key' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(770,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(771,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(56,29): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\nsrc/Services/binanceService.ts(56,35): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(56,40): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(56,51): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(56,59): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(58,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(63,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(63,36): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(63,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(63,64): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(63,78): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(64,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(64,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(67,13): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(67,39): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(67,41): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(84,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(87,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(87,11): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(87,45): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(87,64): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(87,73): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(87,87): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(88,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(88,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(90,9): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(91,16): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(91,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(91,57): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(91,58): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(110,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(110,29): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(110,35): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(110,46): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(110,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(110,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(110,57): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(110,74): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(110,75): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(110,97): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(112,17): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,24): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,38): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,44): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,55): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(112,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(112,65): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(112,66): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(119,18): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(119,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(121,20): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(121,37): error TS1161: Unterminated regular expression literal.\nsrc/Services/binanceService.ts(155,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(158,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(158,35): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(158,54): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(158,71): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(158,77): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(159,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(159,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(160,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(160,32): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(162,9): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(168,5): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(168,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(168,57): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(171,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(178,6): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(181,9): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(181,43): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(183,18): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(183,20): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(183,22): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(191,7): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(207,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(210,24): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(210,43): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(210,58): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(210,73): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(210,82): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(210,84): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(210,86): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(219,22): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(219,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(219,50): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(219,52): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(226,33): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(226,52): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(226,70): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(226,99): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(226,100): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(227,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(227,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(229,9): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(229,14): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(229,40): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(229,42): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(240,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(243,37): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(243,56): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(243,74): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(243,103): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(243,104): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(244,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(244,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(245,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(245,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(267,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(267,37): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(267,52): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(267,61): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(267,63): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(267,65): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(268,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(268,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(281,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(281,24): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(281,48): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(282,9): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(283,16): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(285,7): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(285,13): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(288,9): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(290,13): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(313,10): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,15): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(315,33): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,42): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,50): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,58): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,63): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,70): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(334,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(334,20): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,33): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,38): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,52): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(334,53): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(334,74): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(334,78): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(334,82): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,87): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,96): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,104): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(335,24): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,34): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,40): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,45): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,59): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(335,60): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(337,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(337,23): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(337,31): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(337,38): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(337,48): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(337,49): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(345,41): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(345,42): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(345,68): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(363,17): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(363,23): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(363,34): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(363,34): error TS1351: An identifier or keyword cannot immediately follow a numeric literal.\nsrc/Services/binanceService.ts(363,37): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(363,48): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(363,49): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(382,15): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(382,25): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(382,43): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(382,51): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(382,56): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(382,71): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(416,17): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(416,26): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(416,37): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(416,38): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(416,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(416,48): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(427,41): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(427,56): error TS1161: Unterminated regular expression literal.\nsrc/Services/binanceService.ts(427,76): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(442,5): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(442,7): error TS1005: 'try' expected.\nsrc/Services/binanceService.ts(454,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(457,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(457,47): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(457,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(457,58): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(470,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(471,11): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(472,14): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(473,10): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(474,4): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(474,21): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(474,23): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(475,9): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(477,15): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(477,47): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(477,49): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(509,5): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(509,7): error TS1005: 'try' expected.\nsrc/Services/binanceService.ts(513,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(516,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(516,40): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(516,49): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(516,62): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(516,71): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(516,73): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(516,87): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(517,13): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(517,57): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(517,66): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(517,74): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(517,78): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(517,109): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(525,23): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(525,30): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(525,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(525,50): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(525,60): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,14): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,41): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,44): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,50): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,59): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(543,60): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,68): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(543,77): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,85): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(543,89): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,120): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(589,20): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(589,37): error TS1161: Unterminated regular expression literal.\nsrc/Services/binanceService.ts(659,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(662,37): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(662,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(715,26): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(715,44): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(715,75): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(715,77): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(735,30): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(735,48): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(735,79): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(735,81): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(746,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(746,35): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(746,49): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(746,66): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(760,24): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(760,33): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(760,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(765,22): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(773,1): error TS1128: Declaration or statement expected.\n","stack":"Error: ⨯ Unable to compile TypeScript:\nsrc/Services/binanceService.ts(56,30): error TS2304: Cannot find name 'more'.\nsrc/Services/binanceService.ts(56,35): error TS2304: Cannot find name 'than'.\nsrc/Services/binanceService.ts(56,40): error TS2304: Cannot find name 'needed'.\nsrc/Services/binanceService.ts(56,51): error TS2304: Cannot find name 'analysis'.\nsrc/Services/binanceService.ts(58,3): error TS2304: Cannot find name 'constructor'.\nsrc/Services/binanceService.ts(59,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(63,3): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(63,9): error TS2304: Cannot find name 'subscribeToKlineData'.\nsrc/Services/binanceService.ts(63,30): error TS2552: Cannot find name 'symbol'. Did you mean 'Symbol'?\nsrc/Services/binanceService.ts(63,38): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(63,46): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(63,57): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(63,66): error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ const: string; if(this: { const: string; if(this: ...): any; \"\": any; }): any; \"\": any; }'.\nsrc/Services/binanceService.ts(63,74): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(64,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(64,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(64,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(67,35): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(68,56): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(68,66): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(74,13): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(74,40): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(74,48): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(77,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(77,38): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(77,46): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(79,60): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(79,70): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(81,57): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(81,67): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(87,11): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(87,17): error TS2304: Cannot find name 'initializeKlineBuffer'.\nsrc/Services/binanceService.ts(87,39): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(87,47): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(87,55): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(87,66): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(87,75): error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ const: string; try: { logDebug(: any, symbol: any): any; }; $: any; }'.\nsrc/Services/binanceService.ts(87,83): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(88,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(88,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(88,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(91,16): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(91,49): error TS7006: Parameter 'symbol' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(91,57): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(91,59): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(110,17): error TS2304: Cannot find name 'Initialized'.\nsrc/Services/binanceService.ts(110,29): error TS2304: Cannot find name 'kline'.\nsrc/Services/binanceService.ts(110,35): error TS2304: Cannot find name 'buffer'.\nsrc/Services/binanceService.ts(110,46): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(110,47): error TS2872: This kind of expression is always truthy.\nsrc/Services/binanceService.ts(110,48): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(110,56): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(110,58): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(110,69): error TS1101: 'with' statements are not allowed in strict mode.\nsrc/Services/binanceService.ts(110,74): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(110,90): error TS2304: Cannot find name 'candles'.\nsrc/Services/binanceService.ts(112,17): error TS2304: Cannot find name 'Failed'.\nsrc/Services/binanceService.ts(112,24): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(112,27): error TS2304: Cannot find name 'initialize'.\nsrc/Services/binanceService.ts(112,38): error TS2304: Cannot find name 'kline'.\nsrc/Services/binanceService.ts(112,44): error TS2304: Cannot find name 'buffer'.\nsrc/Services/binanceService.ts(112,55): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(112,56): error TS2872: This kind of expression is always truthy.\nsrc/Services/binanceService.ts(112,57): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(112,65): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(112,67): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(119,18): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(119,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(119,27): error TS2304: Cannot find name '_$'.\nsrc/Services/binanceService.ts(119,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(121,20): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(121,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(123,11): error TS2451: Cannot redeclare block-scoped variable 'ws'.\nsrc/Services/binanceService.ts(123,30): error TS2304: Cannot find name 'wsUrl'.\nsrc/Services/binanceService.ts(126,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(126,62): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(132,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(132,32): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(132,40): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(134,63): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(134,73): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(139,45): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(139,55): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(143,56): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(143,66): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(144,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(144,36): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(148,14): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(148,40): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(149,11): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(149,42): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(149,50): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(154,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(154,31): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(158,11): error TS2304: Cannot find name 'handleKlineUpdate'.\nsrc/Services/binanceService.ts(158,29): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(158,37): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(158,45): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(158,56): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(158,64): error TS2304: Cannot find name 'message'.\nsrc/Services/binanceService.ts(158,73): error TS2693: 'any' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(159,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(159,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(159,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(160,11): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(160,23): error TS2304: Cannot find name 'message'.\nsrc/Services/binanceService.ts(162,9): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(162,10): error TS7006: Parameter 'klineData' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(163,51): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(163,61): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(168,11): error TS2304: Cannot find name 'currentBuffer'.\nsrc/Services/binanceService.ts(168,27): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(168,47): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(171,11): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(172,7): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(173,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(174,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(175,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(176,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(177,18): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(181,5): error TS18004: No value exists in scope for the shorthand property 'let'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(181,29): error TS2304: Cannot find name 'currentBuffer'.\nsrc/Services/binanceService.ts(183,9): error TS7006: Parameter 'klineData' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(185,7): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(185,26): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(188,11): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(188,34): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(189,9): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(189,25): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(189,46): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(193,11): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(194,9): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(194,23): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(194,51): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(196,9): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(196,28): error TS2304: Cannot find name 'newCandle'.\nsrc/Services/binanceService.ts(201,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(201,25): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(201,30): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(204,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(204,33): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(204,38): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(206,40): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(206,50): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(206,77): error TS2304: Cannot find name 'updatedBuffer'.\nsrc/Services/binanceService.ts(206,110): error TS2304: Cannot find name 'klineData'.\nsrc/Services/binanceService.ts(210,3): error TS2304: Cannot find name 'getCachedOHLCV'.\nsrc/Services/binanceService.ts(210,18): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(210,26): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(210,34): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(210,45): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(210,53): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(210,60): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(210,75): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(211,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(211,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(212,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(219,3): error TS2304: Cannot find name 'hasKlineData'.\nsrc/Services/binanceService.ts(219,16): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(219,24): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(219,32): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(219,43): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(219,52): error TS2693: 'boolean' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(220,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(220,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(221,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(226,3): error TS2304: Cannot find name 'subscribeToKlineUpdates'.\nsrc/Services/binanceService.ts(226,27): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(226,35): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(226,43): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(226,54): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(226,62): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(227,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(227,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(227,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(229,9): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(229,10): error TS2680: A 'this' parameter must be the first parameter.\nsrc/Services/binanceService.ts(229,36): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(230,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(230,33): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(233,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(233,31): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(233,41): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(236,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(236,40): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(238,7): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(243,3): error TS2304: Cannot find name 'unsubscribeFromKlineUpdates'.\nsrc/Services/binanceService.ts(243,31): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(243,39): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(243,47): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(243,58): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(243,66): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(244,11): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(244,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(244,30): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(245,11): error TS2304: Cannot find name 'subscribers'.\nsrc/Services/binanceService.ts(245,25): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(245,51): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(247,9): error TS7006: Parameter 'subscribers' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(248,26): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(252,14): error TS2339: Property 'klineSubscribers' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(252,38): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(255,25): error TS2339: Property 'klineConnections' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(255,46): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(258,16): error TS2339: Property 'klineConnections' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(258,40): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(259,16): error TS2339: Property 'klineCache' does not exist on type '{ const: any; if(subscribers: any): void; }'.\nsrc/Services/binanceService.ts(259,34): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(260,50): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(260,60): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(267,11): error TS2304: Cannot find name 'notifyKlineSubscribers'.\nsrc/Services/binanceService.ts(267,34): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(267,39): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(267,47): error TS2304: Cannot find name 'ohlcv'.\nsrc/Services/binanceService.ts(267,54): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(268,11): error TS2304: Cannot find name 'subscribers'.\nsrc/Services/binanceService.ts(268,25): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(268,51): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(269,9): error TS7006: Parameter 'subscribers' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(270,27): error TS7006: Parameter 'callback' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(272,20): error TS2304: Cannot find name 'ohlcv'.\nsrc/Services/binanceService.ts(274,60): error TS2304: Cannot find name 'key'.\nsrc/Services/binanceService.ts(281,3): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(281,9): error TS2304: Cannot find name 'getAllSymbols'.\nsrc/Services/binanceService.ts(281,34): error TS2693: 'BinanceSymbol' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(281,51): error TS2349: This expression is not callable.\n  Type '{ try: { logDebug(: any): any; const: any; logInfo(: any, symbols: any): any; \"\": any; }; trading: any; symbols: any; from: any; Binance: any; }' has no call signatures.\nsrc/Services/binanceService.ts(283,16): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(285,14): error TS2304: Cannot find name 'exchangeInfo'.\nsrc/Services/binanceService.ts(285,28): error TS2304: Cannot find name 'ticker24hr'.\nsrc/Services/binanceService.ts(286,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(287,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(290,13): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(290,23): error TS2304: Cannot find name 'exchangeInfo'.\nsrc/Services/binanceService.ts(300,30): error TS2304: Cannot find name 'ticker24hr'.\nsrc/Services/binanceService.ts(315,15): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(315,26): error TS7006: Parameter 'symbols' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(315,42): error TS18004: No value exists in scope for the shorthand property 'trading'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(315,50): error TS18004: No value exists in scope for the shorthand property 'symbols'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(315,58): error TS18004: No value exists in scope for the shorthand property 'from'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(315,63): error TS18004: No value exists in scope for the shorthand property 'Binance'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(334,17): error TS2304: Cannot find name 'No'.\nsrc/Services/binanceService.ts(334,27): error TS2304: Cannot find name 'price'.\nsrc/Services/binanceService.ts(334,33): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(334,38): error TS2304: Cannot find name 'available'.\nsrc/Services/binanceService.ts(334,52): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(334,53): error TS2362: The left-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.\nsrc/Services/binanceService.ts(334,54): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(334,64): error TS2363: The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.\nsrc/Services/binanceService.ts(334,74): error TS2304: Cannot find name 'may'.\nsrc/Services/binanceService.ts(334,78): error TS2304: Cannot find name 'not'.\nsrc/Services/binanceService.ts(334,82): error TS2304: Cannot find name 'have'.\nsrc/Services/binanceService.ts(334,87): error TS2304: Cannot find name 'received'.\nsrc/Services/binanceService.ts(334,96): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(334,101): error TS2304: Cannot find name 'yet'.\nsrc/Services/binanceService.ts(335,24): error TS2304: Cannot find name 'No'.\nsrc/Services/binanceService.ts(335,34): error TS2304: Cannot find name 'price'.\nsrc/Services/binanceService.ts(335,40): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(335,45): error TS2304: Cannot find name 'available'.\nsrc/Services/binanceService.ts(335,59): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(335,60): error TS2349: This expression is not callable.\n  Type '{ symbol: any; }' has no call signatures.\nsrc/Services/binanceService.ts(335,61): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(337,23): error TS2304: Cannot find name 'getting'.\nsrc/Services/binanceService.ts(337,38): error TS2304: Cannot find name 'price'.\nsrc/Services/binanceService.ts(337,48): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(337,49): error TS2349: This expression is not callable.\n  Type '{ symbol: any; }' has no call signatures.\nsrc/Services/binanceService.ts(337,50): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(345,41): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(345,43): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(345,56): error TS2349: This expression is not callable.\n  Type 'RegExp' has no call signatures.\nsrc/Services/binanceService.ts(363,23): error TS2304: Cannot find name 'fetching'.\nsrc/Services/binanceService.ts(363,34): error TS2304: Cannot find name 'hr'.\nsrc/Services/binanceService.ts(363,37): error TS2304: Cannot find name 'ticker'.\nsrc/Services/binanceService.ts(363,48): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(363,49): error TS2349: This expression is not callable.\n  Type '{ symbol: any; }' has no call signatures.\nsrc/Services/binanceService.ts(363,50): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(382,15): error TS2304: Cannot find name 'Retrieved'.\nsrc/Services/binanceService.ts(382,25): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(382,27): error TS2304: Cannot find name 'tickers'.\nsrc/Services/binanceService.ts(382,43): error TS2304: Cannot find name 'tickers'.\nsrc/Services/binanceService.ts(382,51): error TS2304: Cannot find name 'from'.\nsrc/Services/binanceService.ts(382,66): error TS2304: Cannot find name 'cache'.\nsrc/Services/binanceService.ts(416,17): error TS2304: Cannot find name 'Fetching'.\nsrc/Services/binanceService.ts(416,26): error TS2448: Block-scoped variable 'klines' used before its declaration.\nsrc/Services/binanceService.ts(416,37): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(416,38): error TS2872: This kind of expression is always truthy.\nsrc/Services/binanceService.ts(416,39): error TS18004: No value exists in scope for the shorthand property 'symbol'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(416,47): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(416,49): error TS2448: Block-scoped variable 'interval' used before its declaration.\nsrc/Services/binanceService.ts(427,41): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(427,43): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(444,45): error TS2304: Cannot find name 'retryCount'.\nsrc/Services/binanceService.ts(445,35): error TS2304: Cannot find name 'retryCount'.\nsrc/Services/binanceService.ts(446,38): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(446,81): error TS2304: Cannot find name 'retryCount'.\nsrc/Services/binanceService.ts(451,45): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(457,11): error TS2304: Cannot find name 'convertTimeframeToInterval'.\nsrc/Services/binanceService.ts(457,38): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(457,49): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(457,58): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(470,3): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(470,9): error TS2304: Cannot find name 'getOHLCV'.\nsrc/Services/binanceService.ts(471,5): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(471,13): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(472,5): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(472,16): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(473,5): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(473,12): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(474,6): error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ try: { if(this: { if(this: ...): any; \"\": any; }): any; \"\": any; }; }'.\nsrc/Services/binanceService.ts(474,14): error TS2693: 'number' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(477,29): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(477,37): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(478,28): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(478,48): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(478,56): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(478,67): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(479,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(480,93): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(480,103): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(480,149): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(485,38): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(485,48): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(486,13): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(486,39): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(486,47): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(489,26): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(489,46): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(489,54): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(489,65): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(491,101): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(491,111): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(496,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(497,49): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(497,59): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(497,138): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(498,24): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(498,56): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(499,28): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(499,43): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(499,61): error TS2304: Cannot find name 'limit'.\nsrc/Services/binanceService.ts(510,43): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(516,3): error TS2304: Cannot find name 'async'.\nsrc/Services/binanceService.ts(516,9): error TS2304: Cannot find name 'preSubscribeToKlineData'.\nsrc/Services/binanceService.ts(516,33): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(516,42): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(516,52): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(516,64): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(516,75): error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ logInfo(: any, symbols: any): any; \"\": any; }'.\nsrc/Services/binanceService.ts(516,83): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(517,13): error TS7006: Parameter '(Missing)' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(517,50): error TS7006: Parameter 'symbols' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(517,66): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(517,74): error TS2304: Cannot find name 'and'.\nsrc/Services/binanceService.ts(517,78): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(517,80): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(517,99): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(525,23): error TS2304: Cannot find name 'Failed'.\nsrc/Services/binanceService.ts(525,30): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(525,33): error TS2304: Cannot find name 'pre'.\nsrc/Services/binanceService.ts(525,37): error TS2304: Cannot find name 'subscribe'.\nsrc/Services/binanceService.ts(525,47): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(525,50): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(525,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(525,60): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(525,62): error TS2304: Cannot find name 'timeframe'.\nsrc/Services/binanceService.ts(543,14): error TS2304: Cannot find name 'Completed'.\nsrc/Services/binanceService.ts(543,24): error TS2304: Cannot find name 'pre'.\nsrc/Services/binanceService.ts(543,28): error TS2304: Cannot find name 'subscription'.\nsrc/Services/binanceService.ts(543,41): error TS2304: Cannot find name 'to'.\nsrc/Services/binanceService.ts(543,44): error TS2304: Cannot find name 'kline'.\nsrc/Services/binanceService.ts(543,50): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(543,59): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(543,60): error TS2872: This kind of expression is always truthy.\nsrc/Services/binanceService.ts(543,61): error TS18004: No value exists in scope for the shorthand property 'symbols'. Either declare one or provide an initializer.\nsrc/Services/binanceService.ts(543,77): error TS2304: Cannot find name 'symbols'.\nsrc/Services/binanceService.ts(543,85): error TS2304: Cannot find name 'and'.\nsrc/Services/binanceService.ts(543,89): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(543,91): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(543,110): error TS2304: Cannot find name 'timeframes'.\nsrc/Services/binanceService.ts(589,20): error TS2581: Cannot find name '$'. Do you need to install type definitions for jQuery? Try `npm i --save-dev @types/jquery`.\nsrc/Services/binanceService.ts(589,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(590,11): error TS2451: Cannot redeclare block-scoped variable 'ws'.\nsrc/Services/binanceService.ts(590,30): error TS2304: Cannot find name 'wsUrl'.\nsrc/Services/binanceService.ts(594,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(623,15): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(627,15): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(632,11): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(632,46): error TS7006: Parameter 'callback' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(649,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(654,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(655,24): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(658,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(662,3): error TS2304: Cannot find name 'subscribeToIndividualTicker'.\nsrc/Services/binanceService.ts(662,31): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(662,39): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(663,37): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(666,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(670,22): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(670,40): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(674,53): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(680,46): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(692,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(692,34): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(695,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(695,32): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(697,57): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(702,47): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(706,58): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(707,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(708,24): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(708,57): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(711,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(715,3): error TS2304: Cannot find name 'subscribeToPrice'.\nsrc/Services/binanceService.ts(715,20): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(715,28): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(715,36): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(716,10): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(716,31): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(717,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(717,28): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(719,7): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(719,40): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(721,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(721,26): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(721,39): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(724,20): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(724,45): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(726,7): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(730,38): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(735,3): error TS2304: Cannot find name 'unsubscribeFromPrice'.\nsrc/Services/binanceService.ts(735,24): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(735,32): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(735,40): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(736,31): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(736,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(738,32): error TS2304: Cannot find name 'callback'.\nsrc/Services/binanceService.ts(740,9): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(740,33): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(746,11): error TS2304: Cannot find name 'notifySubscribers'.\nsrc/Services/binanceService.ts(746,29): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(746,37): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(746,45): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(746,51): error TS2693: 'BinanceTicker' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(747,31): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(747,52): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(749,33): error TS7006: Parameter 'callback' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(751,20): error TS2304: Cannot find name 'data'.\nsrc/Services/binanceService.ts(753,58): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(760,3): error TS2304: Cannot find name 'getCachedPrice'.\nsrc/Services/binanceService.ts(760,18): error TS2304: Cannot find name 'symbol'.\nsrc/Services/binanceService.ts(760,26): error TS2693: 'string' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(760,35): error TS2693: 'BinanceTicker' only refers to a type, but is being used as a value here.\nsrc/Services/binanceService.ts(760,51): error TS18050: The value 'null' cannot be used here.\nsrc/Services/binanceService.ts(765,3): error TS2304: Cannot find name 'closeConnections'.\nsrc/Services/binanceService.ts(766,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(766,33): error TS7006: Parameter 'ws' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(766,37): error TS7006: Parameter 'key' implicitly has an 'any' type.\nsrc/Services/binanceService.ts(770,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(771,5): error TS2532: Object is possibly 'undefined'.\nsrc/Services/binanceService.ts(56,29): error TS1068: Unexpected token. A constructor, method, accessor, or property was expected.\nsrc/Services/binanceService.ts(56,35): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(56,40): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(56,51): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(56,59): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(58,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(63,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(63,36): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(63,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(63,64): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(63,78): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(64,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(64,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(67,13): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(67,39): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(67,41): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(84,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(87,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(87,11): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(87,45): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(87,64): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(87,73): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(87,87): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(88,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(88,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(90,9): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(91,16): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(91,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(91,57): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(91,58): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(110,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(110,29): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(110,35): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(110,46): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(110,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(110,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(110,57): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(110,74): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(110,75): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(110,97): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(112,17): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,24): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,38): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,44): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(112,55): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(112,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(112,65): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(112,66): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(119,18): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(119,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(121,20): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(121,37): error TS1161: Unterminated regular expression literal.\nsrc/Services/binanceService.ts(155,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(158,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(158,35): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(158,54): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(158,71): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(158,77): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(159,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(159,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(160,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(160,32): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(162,9): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(168,5): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(168,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(168,57): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(171,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(178,6): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(181,9): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(181,43): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(183,18): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(183,20): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(183,22): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(191,7): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(207,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(210,24): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(210,43): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(210,58): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(210,73): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(210,82): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(210,84): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(210,86): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(219,22): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(219,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(219,50): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(219,52): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(226,33): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(226,52): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(226,70): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(226,99): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(226,100): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(227,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(227,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(229,9): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(229,14): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(229,40): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(229,42): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(240,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(243,37): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(243,56): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(243,74): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(243,103): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(243,104): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(244,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(244,41): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(245,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(245,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(267,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(267,37): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(267,52): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(267,61): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(267,63): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(267,65): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(268,11): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(268,55): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(281,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(281,24): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(281,48): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(282,9): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(283,16): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(285,7): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(285,13): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(288,9): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(290,13): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(313,10): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,15): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(315,33): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,42): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,50): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,58): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,63): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(315,70): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(334,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(334,20): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,33): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,38): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,52): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(334,53): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(334,74): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(334,78): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(334,82): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,87): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,96): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(334,104): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(335,24): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,27): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,34): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,40): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,45): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(335,59): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(335,60): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(337,17): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(337,23): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(337,31): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(337,38): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(337,48): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(337,49): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(345,41): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(345,42): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(345,68): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(363,17): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(363,23): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(363,34): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(363,34): error TS1351: An identifier or keyword cannot immediately follow a numeric literal.\nsrc/Services/binanceService.ts(363,37): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(363,48): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(363,49): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(382,15): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(382,25): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(382,43): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(382,51): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(382,56): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(382,71): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(416,17): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(416,26): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(416,37): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(416,38): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(416,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(416,48): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(427,41): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(427,56): error TS1161: Unterminated regular expression literal.\nsrc/Services/binanceService.ts(427,76): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(442,5): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(442,7): error TS1005: 'try' expected.\nsrc/Services/binanceService.ts(454,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(457,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(457,47): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(457,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(457,58): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(470,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(471,11): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(472,14): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(473,10): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(474,4): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(474,21): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(474,23): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(475,9): error TS1005: ':' expected.\nsrc/Services/binanceService.ts(477,15): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(477,47): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(477,49): error TS1136: Property assignment expected.\nsrc/Services/binanceService.ts(509,5): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(509,7): error TS1005: 'try' expected.\nsrc/Services/binanceService.ts(513,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(516,3): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(516,40): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(516,49): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(516,62): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(516,71): error TS1011: An element access expression should take an argument.\nsrc/Services/binanceService.ts(516,73): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(516,87): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(517,13): error TS1003: Identifier expected.\nsrc/Services/binanceService.ts(517,57): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(517,66): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(517,74): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(517,78): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(517,109): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(525,23): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(525,30): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(525,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(525,50): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(525,60): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,14): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,41): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,44): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,50): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,59): error TS1005: '(' expected.\nsrc/Services/binanceService.ts(543,60): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,68): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(543,77): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(543,85): error TS1005: ')' expected.\nsrc/Services/binanceService.ts(543,89): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(543,120): error TS1443: Module declaration names may only use ' or \" quoted strings.\nsrc/Services/binanceService.ts(589,20): error TS1434: Unexpected keyword or identifier.\nsrc/Services/binanceService.ts(589,37): error TS1161: Unterminated regular expression literal.\nsrc/Services/binanceService.ts(659,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(662,37): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(662,47): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(715,26): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(715,44): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(715,75): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(715,77): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(735,30): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(735,48): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(735,79): error TS1109: Expression expected.\nsrc/Services/binanceService.ts(735,81): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(746,3): error TS1128: Declaration or statement expected.\nsrc/Services/binanceService.ts(746,35): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(746,49): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(746,66): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(760,24): error TS1005: ',' expected.\nsrc/Services/binanceService.ts(760,33): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(760,56): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(765,22): error TS1005: ';' expected.\nsrc/Services/binanceService.ts(773,1): error TS1128: Declaration or statement expected.\n\n    at Object.<anonymous> (/Users/<USER>/Documents/GitHub/Crypto_Assistant/backend/src/Services/binanceService.ts:1:7)\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1554:14)\n    at Module._compile (/Users/<USER>/Documents/GitHub/Crypto_Assistant/backend/node_modules/source-map-support/source-map-support.js:568:25)\n    at Module.m._compile (/private/var/folders/lz/2lzggqrn6lzbvwy3x7jt253h0000gn/T/ts-node-dev-hook-750081248972637.js:69:33)\n    at node:internal/modules/cjs/loader:1706:10\n    at require.extensions..jsx.require.extensions..js (/private/var/folders/lz/2lzggqrn6lzbvwy3x7jt253h0000gn/T/ts-node-dev-hook-750081248972637.js:114:20)\n    at require.extensions.<computed> (/private/var/folders/lz/2lzggqrn6lzbvwy3x7jt253h0000gn/T/ts-node-dev-hook-750081248972637.js:71:20)\n    at Object.nodeDevHook [as .ts] (/Users/<USER>/Documents/GitHub/Crypto_Assistant/backend/node_modules/ts-node-dev/lib/hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)","timestamp":"2025-07-25 18:13:18:1318"}