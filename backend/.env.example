# Environment Configuration Template
# Copy this file to .env and fill in your values

# Application
NODE_ENV=production
PORT=5001

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,https://crypto-assistant-tan.vercel.app

# Database (Optional - for future use)
DATABASE_URL=
REDIS_URL=

# Exchange API Keys (Optional - for enhanced features)
BINANCE_API_KEY=
BINANCE_SECRET_KEY=
COINBASE_API_KEY=
COINBASE_SECRET_KEY=
KRAKEN_API_KEY=
KRAKEN_SECRET_KEY=

# External API Keys (Optional - for enhanced data)
COINGECKO_API_KEY=
CRYPTOCOMPARE_API_KEY=

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=24h

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# WebSocket
WS_PORT=5002

# Signal Processing
SIGNAL_UPDATE_INTERVAL=60000
MAX_CANDLE_HISTORY=1000
